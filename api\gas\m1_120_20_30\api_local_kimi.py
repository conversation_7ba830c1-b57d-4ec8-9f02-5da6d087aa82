# file: api_local.py (最终无状态预测版 - 已修复 content 未定义 BUG)

# 1. 导入必要的库
import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
import numpy as np
import torch
import os
import argparse
from typing import Dict, List
from collections import deque
from contextlib import asynccontextmanager

from models import TimeLLM
from utils.tools import load_content  # 确保 load_content 被导入
from decimal import Decimal, ROUND_HALF_UP
# 2. 定义 FastAPI 应用
app = FastAPI(
    title="无状态时序预测 API",
    description="每次调用接收完整的历史序列，返回独立的预测结果。",
    version="5.1.0"  # 修复版本
)

# 3. ---------------- 模型配置与特征顺序 (保持不变) ----------------
GAS_FEATURE_ORDER = [
    '66081101194701MN0001001A090', '66081101194701MN0001002A010', '66081101194701MN0001002A040',
    '66081101194701MN0001003A010', '66081101194701MN0001003A020', '66081101194701MN0001003A030',
    '66081101194701MN0001003A050', '66081101194701MN0001005A080', '66081101194701MN0001005A090',
    '66081101194701MN0001005A130', '66081101194701MN0001008A150', '66081101194701MN0001009A070',
    '66081101194701MN0001009A130', '66081101194701MN0001010A010', '66081101194701MN0001010A090',
    '66081101194701MN0001012A010', '66081101194701MN0001012A050', '66081101194701MN0001012A110',
    '66081101194701MN0001012A130', '66081101194701MN0001013A090', '66081101194701MN0001015A090',
    '66081101194701MN0001015A130', '66081101194701MN0001017A110', '66081101194701MN0001017A160',
    '66081101194701MN0001018A090', '66081101194701MN0001018A100', '66081101194701MN0001018A130',
    '66081101194701MN0001021A080', '66081101194701MN0001023A090', '66081101194701MN0001023A130'
]
WEATHER_FEATURE_ORDER = [
    'p (mbar)', 'T (degC)', 'Tpot (K)', 'Tdew (degC)', 'rh (%)', 'VPmax (mbar)', 'VPact (mbar)', 'VPdef (mbar)',
    'sh (g/kg)', 'H2OC (mmol/mol)', 'rho (g/m**3)', 'wv (m/s)', 'max. wv (m/s)', 'wd (deg)', 'rain (mm)', 'raining (s)',
    'SWDR (W/m?)', 'PAR (?mol/m?/s)', 'max. PAR (?mol/m?/s)', 'Tlog (degC)', 'OT'
]


class Args(argparse.Namespace):
    def __init__(self, **kwargs):
        defaults = {'patch_len': 16, 'stride': 8, 'output_attention': False, 'n_heads': 8, 'freq': 't'}
        defaults.update(kwargs)
        super().__init__(**defaults)


MODELS_CONFIG: Dict[str, dict] = {
    "gas": {
        "use_model_parallel": False,
        "model_path": "./checkpoints/gas/gas_m1_120_20_30/checkpoint.pth",
        "feature_order": GAS_FEATURE_ORDER,
        "model": 'TimeLLM',
        "task_name": 'long_term_forecast',
        "seq_len": 120,
        "label_len": 20,
        "pred_len": 30,
        "e_layers": 2,
        "d_layers": 1,
        "factor": 1,
        "enc_in": 30,
        "dec_in": 30,
        "c_out": 30,
        "d_model": 32,
        "d_ff": 128,
        "llm_layers": 32,
        "llm_model": 'LLAMA',
        "llm_dim": 4096,
        "data": 'custom',
        "features": 'M',
        "target": 'ch4_face_return_air_0308',
        "embed": 'timeF',
        "root_path": "./dataset/gas/",
        "data_path": "gas_custom.csv",
        "patch_len": 16,
        "prompt_domain": True,
        "prompt_str": "The dataset records gas concentration every minute, comprising 30 indicators representing the gas concentration levels at different monitoring points.",
        "content": "The dataset records gas concentration every minute, comprising 30 indicators representing the gas concentration levels at different monitoring points.",
        "stride": 8,
        "output_attention": False,
        "dropout": 0.1,
        "post_processing": {
            "clip_min": 0.0,      # 瓦斯浓度的物理下限
            "clip_max": 5.0,    # 瓦斯浓度的安全或物理上限 (例如 100%)
            "fill_nan": 0.0       # 如果出现NaN值，用0填充
        },
    },
    # "weather": {
    #     "use_model_parallel": False,
    #     "model_path": "./checkpoints/weather/weather_m10_512_48_96/checkpoint.pth",
    #     "feature_order": WEATHER_FEATURE_ORDER,
    #     "model": 'TimeLLM',
    #     "task_name": 'long_term_forecast',
    #     "seq_len": 512,
    #     "label_len": 48,
    #     "pred_len": 96,
    #     "e_layers": 2,
    #     "d_layers": 1,
    #     "factor": 3,
    #     "enc_in": 21,
    #     "dec_in": 21,
    #     "c_out": 21,
    #     "d_model": 32,
    #     "d_ff": 32,
    #     "llm_layers": 32,
    #     "llm_model": 'LLAMA',
    #     "llm_dim": 4096,
    #     "dropout": 0.1,
    #     "patch_len": 16,
    #     "prompt_domain": True,
    #     "data": 'Weather',
    #     "features": 'M',
    #     "target": 'OT',
    #     "freq": 'h',
    #     "embed": 'timeF',
    #     "root_path": "./dataset/weather/",
    #     "data_path": "weather.csv",
    #     "prompt_str": "Weather is recorded every 10 minutes for the 2020 whole year, which contains 21 meteorological indicators, such as air temperature, humidity, etc.",
    #     "stride": 8,
    #     "output_attention": False,
    #     "activation": "gelu",
    #     "post_processing": {
        #     "clip_min": -50.0,    # 天气温度可能的下限
        #     "clip_max": 60.0,     # 天气温度可能的上限
        #     "fill_nan": 0.0
    #     },
    # },
}

# 4. ---------------- 模型加载与缓存 (无状态) ----------------
# 缓存结构: { model_name: (model, args, content) }
LOADED_MODELS: Dict[str, tuple] = {}
DEVICE = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")


def initialize_model_and_data(model_name: str):
    config_dict = MODELS_CONFIG[model_name]
    args = Args(**config_dict)

    # --- 不再创建 data_buffer ---

    if not hasattr(args, 'content'):
        args.content = load_content(args)

    model_path = config_dict["model_path"]
    if not os.path.exists(model_path): raise FileNotFoundError(f"找不到模型文件: {model_path}")

    model = TimeLLM.Model(args).float()
    model.load_state_dict(torch.load(model_path, map_location=DEVICE))
    model.to(DEVICE)
    model.eval()

    # --- 缓存中不再包含 data_buffer ---
    LOADED_MODELS[model_name] = (model, args, args.content)
    print(f"🎉 模型 '{model_name}' 已成功初始化 (无状态模式)！")


@asynccontextmanager
async def lifespan(app: FastAPI):
    print("服务启动 (无状态模式)... 尝试预加载模型。")
    os.makedirs("./final_models/", exist_ok=True)
    for name in MODELS_CONFIG:
        if os.path.exists(MODELS_CONFIG[name]["model_path"]):
            try:
                initialize_model_and_data(name)
            except Exception as e:
                print(f"警告：初始化模型 '{name}' 失败: {e}")
    yield
    print("服务正在关闭... 清理模型缓存。")
    LOADED_MODELS.clear()


app = FastAPI(title="无状态时序预测 API", version="5.1.0", lifespan=lifespan)


# 5. ---------------- API 输入输出定义 ----------------
class PointData(BaseModel):
    point: str
    values: List[float]


class PredictionInput(BaseModel):
    model: str
    data: List[PointData]


class PredictionOutput(BaseModel):
    model: str
    data: List[PointData]


# 6. ---------------- API 端点实现 (无状态) ----------------

# 添加OpenAI兼容端点
@app.get("/")
async def root():
    """根路径"""
    return {"message": "瓦斯浓度预测API", "version": "1.1.0", "docs": "/docs"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "gas_prediction_api"}

@app.get("/v1/models")
async def handle_openai_models():
    """处理OpenAI兼容的模型查询"""
    return {
        "object": "list",
        "data": [
            {
                "id": "gas",
                "object": "model",
                "created": **********,
                "owned_by": "Time-LLM"
            }
        ]
    }

@app.post("/predict", response_model=PredictionOutput, tags=["无状态预测服务"])
async def predict_api(input_data: PredictionInput):
    """
    瓦斯浓度预测API
    
    功能:
    1. 如果request请求参数中某个point的values都是0，那么对应的response输出中这个point的values也需要都是0
    2. response输出大的values中的值对大不能超过MODELS_CONFIG中gas字典设定的clip_max
    3. response输出的values中的值需要四舍五入保留两位小数
    4. response输出需要有高的灵敏度，所以不要基于任何过去的或者缓存的数据来影响预测值
    """
    try:
        model_name = input_data.model
        if model_name not in LOADED_MODELS:
            raise HTTPException(status_code=404, detail=f"模型 '{model_name}' 未找到或未加载")
        
        model, args, content = LOADED_MODELS[model_name]
        config = MODELS_CONFIG[model_name]
        
        # 获取特征顺序
        feature_order = config["feature_order"]
        pred_len = config["pred_len"]
        clip_max = config["post_processing"]["clip_max"]
        
        # 构建输入数据字典
        input_dict = {point_data.point: point_data.values for point_data in input_data.data}
        
        # 检查每个点的数据长度是否符合要求
        seq_len = config["seq_len"]
        for point, values in input_dict.items():
            if len(values) != seq_len:
                raise HTTPException(
                    status_code=400, 
                    detail=f"点 '{point}' 的数据长度应为 {seq_len}，实际为 {len(values)}"
                )
        
        # 构建模型输入张量
        # 创建一个形状为 (1, seq_len, enc_in) 的张量
        input_tensor = np.zeros((1, seq_len, len(feature_order)))
        
        # 填充输入张量
        for i, feature_name in enumerate(feature_order):
            if feature_name in input_dict:
                input_tensor[0, :, i] = input_dict[feature_name]
        
        # 转换为PyTorch张量
        input_tensor = torch.FloatTensor(input_tensor).to(DEVICE)
        
        # 创建批次输入
        batch_x = input_tensor
        batch_x_mark = torch.zeros((1, seq_len, 4)).to(DEVICE)  # 时间特征占位符
        batch_y = torch.zeros((1, config["label_len"] + pred_len, len(feature_order))).to(DEVICE)
        batch_y_mark = torch.zeros((1, config["label_len"] + pred_len, 4)).to(DEVICE)
        
        # 模型预测
        with torch.no_grad():
            outputs = model(batch_x, batch_x_mark, batch_y, batch_y_mark)
            if isinstance(outputs, tuple):
                outputs = outputs[0]
        
        # 处理预测结果
        predictions = outputs.cpu().numpy()[0, -pred_len:, :]  # 形状: (pred_len, c_out)
        
        # 构建响应数据
        response_data = []
        
        for i, feature_name in enumerate(feature_order):
            # 检查该点是否所有值都是0
            if feature_name in input_dict and all(v == 0 for v in input_dict[feature_name]):
                # 如果输入全为0，输出也全为0
                predicted_values = [0.0] * pred_len
            else:
                # 否则使用模型预测结果
                predicted_values = predictions[:, i].tolist()
                
                # 应用clip限制
                predicted_values = [max(0.0, min(clip_max, val)) for val in predicted_values]
                
                # 四舍五入保留两位小数
                predicted_values = [round(val, 2) for val in predicted_values]
            
            response_data.append({
                "point": feature_name,
                "values": predicted_values
            })
        
        return PredictionOutput(
            model=model_name,
            data=[PointData(point=item["point"], values=item["values"]) for item in response_data]
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"预测失败: {str(e)}")


# --- 主函数 ---
if __name__ == '__main__':
    uvicorn.run("api_local_kimi:app", host="0.0.0.0", port=8000, reload=True)
