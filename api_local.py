# file: api_server.py
# 最终现代化版: 严格基于用户提供的v8.0.0版本，仅将 on_event 迁移到 lifespan。

# 1. 导入必要的库
import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
import numpy as np
import torch
import os
import argparse
from typing import Dict, List
from collections import deque
from contextlib import asynccontextmanager # <-- 添加了这行 import

from models import TimeLLM
from utils.tools import load_content
from decimal import Decimal, ROUND_HALF_UP
# (前面所有的 MODELS_CONFIG, Args, GAS_FEATURE_ORDER 等定义都保持原样)
# ...
GAS_FEATURE_ORDER = [
    '66081101194701MN0001001A090', '66081101194701MN0001002A010', '66081101194701MN0001002A040',
    '66081101194701MN0001003A010', '66081101194701MN0001003A020', '66081101194701MN0001003A030',
    '66081101194701MN0001003A050', '66081101194701MN0001005A080', '66081101194701MN0001005A090',
    '66081101194701MN0001005A130', '66081101194701MN0001008A150', '66081101194701MN0001009A070',
    '66081101194701MN0001009A130', '66081101194701MN0001010A010', '66081101194701MN0001010A090',
    '66081101194701MN0001012A010', '66081101194701MN0001012A050', '66081101194701MN0001012A110',
    '66081101194701MN0001012A130', '66081101194701MN0001013A090', '66081101194701MN0001015A090',
    '66081101194701MN0001015A130', '66081101194701MN0001017A110', '66081101194701MN0001017A160',
    '66081101194701MN0001018A090', '66081101194701MN0001018A100', '66081101194701MN0001018A130',
    '66081101194701MN0001021A080', '66081101194701MN0001023A090', '66081101194701MN0001023A130'
]
WEATHER_FEATURE_ORDER = [
    'p (mbar)', 'T (degC)', 'Tpot (K)', 'Tdew (degC)', 'rh (%)', 'VPmax (mbar)', 'VPact (mbar)', 'VPdef (mbar)', 'sh (g/kg)', 'H2OC (mmol/mol)', 'rho (g/m**3)', 'wv (m/s)', 'max. wv (m/s)', 'wd (deg)', 'rain (mm)', 'raining (s)', 'SWDR (W/m?)', 'PAR (?mol/m?/s)', 'max. PAR (?mol/m?/s)', 'Tlog (degC)', 'OT'
]
class Args(argparse.Namespace):
    def __init__(self, **kwargs):
        defaults = {'patch_len': 16, 'stride': 8, 'output_attention': False, 'n_heads': 8}
        defaults.update(kwargs)
        super().__init__(**defaults)

MODELS_CONFIG: Dict[str, dict] = {
    "gas": {
        "use_model_parallel": False,
        "model_path": "./checkpoints/gas/gas_m1_120_20_30/checkpoint.pth",
        "feature_order": GAS_FEATURE_ORDER,
        "model": 'TimeLLM',
        "task_name": 'long_term_forecast',
        "seq_len": 120,
        "label_len": 20,
        "pred_len": 30,
        "e_layers": 2,
        "d_layers": 1,
        "factor": 1,
        "enc_in": 30,
        "dec_in": 30,
        "c_out": 30,
        "d_model": 32,
        "d_ff": 128,
        "llm_layers": 32,
        "llm_model": 'LLAMA',
        "llm_dim": 4096,
        "data": 'custom',
        "features": 'M',
        "target": 'ch4_face_return_air_0308',
        "embed": 'timeF',
        "root_path": "./dataset/gas/",
        "data_path": "gas_custom.csv",
        "patch_len": 16,
        "prompt_domain": True,
        "prompt_str": "The dataset records gas concentration every minute, comprising 30 indicators representing the gas concentration levels at different monitoring points.",
        "content": "The dataset records gas concentration every minute, comprising 30 indicators representing the gas concentration levels at different monitoring points.",
        "stride": 8,
        "output_attention": False,
        "dropout": 0.1,
        "post_processing": {
            "clip_min": 0.0,      # 瓦斯浓度的物理下限
            "clip_max": 5.0,    # 瓦斯浓度的安全或物理上限 (例如 100%)
            "fill_nan": 0.0       # 如果出现NaN值，用0填充
        },
    },
    # "weather": {
    #     "use_model_parallel": False,
    #     "model_path": "./checkpoints/weather/weather_m10_512_48_96/checkpoint.pth",
    #     "feature_order": WEATHER_FEATURE_ORDER,
    #     "model": 'TimeLLM',
    #     "task_name": 'long_term_forecast',
    #     "seq_len": 512,
    #     "label_len": 48,
    #     "pred_len": 96,
    #     "e_layers": 2,
    #     "d_layers": 1,
    #     "factor": 3,
    #     "enc_in": 21,
    #     "dec_in": 21,
    #     "c_out": 21,
    #     "d_model": 32,
    #     "d_ff": 32,
    #     "llm_layers": 32,
    #     "llm_model": 'LLAMA',
    #     "llm_dim": 4096,
    #     "dropout": 0.1,
    #     "patch_len": 16,
    #     "prompt_domain": True,
    #     "data": 'Weather',
    #     "features": 'M',
    #     "target": 'OT',
    #     "freq": 'h',
    #     "embed": 'timeF',
    #     "root_path": "./dataset/weather/",
    #     "data_path": "weather.csv",
    #     "prompt_str": "Weather is recorded every 10 minutes for the 2020 whole year, which contains 21 meteorological indicators, such as air temperature, humidity, etc.",
    #     "stride": 8,
    #     "output_attention": False,
    #     "activation": "gelu",
    #     "post_processing": {
        #     "clip_min": -50.0,    # 天气温度可能的下限
        #     "clip_max": 60.0,     # 天气温度可能的上限
        #     "fill_nan": 0.0
    #     },
    # },
}
LOADED_MODELS: Dict[str, tuple] = {}
DEVICE = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")


def initialize_model_and_data(model_name: str):
    config_dict = MODELS_CONFIG[model_name]
    args = Args(**config_dict)

    # 提前加载 content
    if not hasattr(args, 'content'):
        args.content = load_content(args)

    data_buffer = np.zeros((args.seq_len, args.enc_in))
    model_path = config_dict["model_path"]
    if not os.path.exists(model_path): raise FileNotFoundError(f"找不到模型文件: {model_path}")
    print(f"为模型 '{model_name}' 创建模型实例...")
    model = TimeLLM.Model(args).float()
    print(f"正在从 '{model_path}' 加载模型权重...")
    # 对于 device_map="auto" 的模型，加载权重时需要特殊处理
    if getattr(args, 'use_model_parallel', False):
        # 对于并行模型，权重需要加载到CPU，然后由 PyTorch 自动分发
        state_dict = torch.load(model_path, map_location="cpu")
        model.load_state_dict(state_dict, strict=False)
        # 模型已经被自动分配到多个GPU，不需要再 .to(DEVICE)
    else:
        # 对于单卡模型，正常加载
        model.load_state_dict(torch.load(model_path, map_location=DEVICE))
        model.to(DEVICE)

    model.eval()
    LOADED_MODELS[model_name] = (model, args, data_buffer)
    print(f"🎉 模型 '{model_name}' 和数据缓冲区已成功初始化！")

# ======================= 核心修改区域 =======================
# 1. 定义新的 Lifespan 事件处理器
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 这是原来 startup_event 的全部内容
    print("服务启动 (lifespan)... 初始化所有已配置且文件存在的模型。")
    os.makedirs("./checkpoints/gas/", exist_ok=True)
    os.makedirs("./checkpoints/weather/", exist_ok=True)
    for name in MODELS_CONFIG:
        if os.path.exists(MODELS_CONFIG[name]["model_path"]):
            try:
                initialize_model_and_data(name)
            except Exception as e:
                print(f"警告：初始化模型 '{name}' 失败: {e}")
        else:
            print(f"信息：模型文件 for '{name}' 不存在 ({MODELS_CONFIG[name]['model_path']})，跳过初始化。")
    yield
    print("服务正在关闭... 清理模型缓存。")
    LOADED_MODELS.clear()

# 2. 创建 FastAPI 应用实例，并传入 lifespan
app = FastAPI(
    title="智能时序预测 API (最终版)",
    description="接收结构化的、部分最新的数据，服务器自动维护历史序列并返回结构化的预测结果。",
    version="1.0.0",
    lifespan=lifespan # <-- 添加这个参数
)

# 3. 旧的 @app.on_event("startup") 已被上面的 lifespan 完全替代，无需保留
# ==========================================================

# (后续所有 API 定义和端点实现代码，完全保持您提供的版本不变)
class PointData(BaseModel):
    point: str = Field(..., description="监测点名称")
    values: List[float] = Field(..., description="该监测点的一段最新序列数据")

class PredictionInput(BaseModel):
    model: str = Field(..., example="gas")
    data: List[PointData]

class PredictionOutput(BaseModel):
    model: str
    data: List[PointData]

@app.post("/predict", response_model=PredictionOutput, tags=["智能更新预测服务"])
async def predict_api(input_data: PredictionInput):
    model_name = input_data.model
    if model_name not in LOADED_MODELS:
        raise HTTPException(status_code=404, detail=f"模型 '{model_name}' 未加载或初始化失败。")

    model, args, data_buffer = LOADED_MODELS[model_name]
    config = MODELS_CONFIG[model_name]
    feature_order = MODELS_CONFIG[model_name]["feature_order"]

    try:
        input_dict = {item.point: item.values for item in input_data.data}
        if len(input_dict) != args.enc_in:
            raise ValueError(f"需要 {args.enc_in} 个监测点，收到了 {len(input_dict)} 个。")

        # --- 记录输入全为零的监测点 ---
        zero_input_points = {item.point for item in input_data.data if all(v == 0 for v in item.values)}
        print(f"检测到输入全为零的监测点: {zero_input_points if zero_input_points else '无'}")

        ordered_sequences = [input_dict[name] for name in feature_order]
        new_data_array = np.array(ordered_sequences).T
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"解析输入数据时出错: {e}")

    num_new_points = new_data_array.shape[0]
    if num_new_points >= args.seq_len:
        data_buffer[:] = new_data_array[-args.seq_len:]
    else:
        data_buffer[:-num_new_points] = data_buffer[num_new_points:]
        data_buffer[-num_new_points:] = new_data_array

    try:
        # 对于并行模型，输入数据应在CPU上
        device_for_input = "cpu" if config.get("use_model_parallel") else DEVICE
        batch_x = torch.from_numpy(data_buffer).float().unsqueeze(0).to(device_for_input)

        time_mark_dim = 4
        batch_x_mark = torch.zeros((1, args.seq_len, time_mark_dim), device=DEVICE).float()
        batch_y_mark = torch.zeros((1, args.label_len + args.pred_len, time_mark_dim), device=DEVICE).float()
        dec_inp = torch.zeros((1, args.pred_len, args.c_out), device=DEVICE).float()
        dec_inp = torch.cat([batch_x[:, -args.label_len:, :], dec_inp], dim=1).float()

        with torch.no_grad():
            outputs = model(batch_x, batch_x_mark, dec_inp, batch_y_mark)

        prediction_array = outputs.detach().cpu().numpy()[0]  # Shape: [pred_len, c_out]

        # ======================= 关键修复：动态应用后处理规则 =======================
        pp_rules = config.get("post_processing")
        if pp_rules:
            min_val = pp_rules.get("clip_min")
            max_val = pp_rules.get("clip_max")
            fill_val = pp_rules.get("fill_nan")

            if fill_val is not None:
                prediction_array[np.isnan(prediction_array)] = fill_val

            np.clip(prediction_array, min_val, max_val, out=prediction_array)
        # =========================================================================

        prediction_array_transposed = prediction_array.T
        output_data = []
        pred_len = config.get("pred_len")

        if model_name == "gas":
            for i, feature_name in enumerate(feature_order):
                if feature_name in zero_input_points:
                    final_values = [0.0] * pred_len
                else:
                    final_values = [
                        float(Decimal(str(val)).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP))
                        for val in prediction_array_transposed[i]
                    ]

                output_data.append(PointData(point=feature_name, values=final_values))
        else:
            for i, feature_name in enumerate(feature_order):
                output_data.append(PointData(point=feature_name, values=prediction_array_transposed[i].tolist()))

        return PredictionOutput(model=model_name, data=output_data)

    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"预测过程中发生内部错误: {str(e)}")

if __name__ == '__main__':
    uvicorn.run("api_local:app", host="0.0.0.0", port=8000, reload=True)
