# file: api_local.py (最终无状态预测版 - 已修复 content 未定义 BUG)

# 1. 导入必要的库
import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
import numpy as np
import torch
import os
import argparse
from typing import Dict, List
from collections import deque
from contextlib import asynccontextmanager

from models import TimeLLM
from utils.tools import load_content  # 确保 load_content 被导入
from decimal import Decimal, ROUND_HALF_UP
# 2. 定义 FastAPI 应用
app = FastAPI(
    title="无状态时序预测 API",
    description="每次调用接收完整的历史序列，返回独立的预测结果。",
    version="5.1.0"  # 修复版本
)

# 3. ---------------- 模型配置与特征顺序 (保持不变) ----------------
GAS_FEATURE_ORDER = [
    '66081101194701MN0001001A090', '66081101194701MN0001002A010', '66081101194701MN0001002A040',
    '66081101194701MN0001003A010', '66081101194701MN0001003A020', '66081101194701MN0001003A030',
    '66081101194701MN0001003A050', '66081101194701MN0001005A080', '66081101194701MN0001005A090',
    '66081101194701MN0001005A130', '66081101194701MN0001008A150', '66081101194701MN0001009A070',
    '66081101194701MN0001009A130', '66081101194701MN0001010A010', '66081101194701MN0001010A090',
    '66081101194701MN0001012A010', '66081101194701MN0001012A050', '66081101194701MN0001012A110',
    '66081101194701MN0001012A130', '66081101194701MN0001013A090', '66081101194701MN0001015A090',
    '66081101194701MN0001015A130', '66081101194701MN0001017A110', '66081101194701MN0001017A160',
    '66081101194701MN0001018A090', '66081101194701MN0001018A100', '66081101194701MN0001018A130',
    '66081101194701MN0001021A080', '66081101194701MN0001023A090', '66081101194701MN0001023A130'
]
WEATHER_FEATURE_ORDER = [
    'p (mbar)', 'T (degC)', 'Tpot (K)', 'Tdew (degC)', 'rh (%)', 'VPmax (mbar)', 'VPact (mbar)', 'VPdef (mbar)',
    'sh (g/kg)', 'H2OC (mmol/mol)', 'rho (g/m**3)', 'wv (m/s)', 'max. wv (m/s)', 'wd (deg)', 'rain (mm)', 'raining (s)',
    'SWDR (W/m?)', 'PAR (?mol/m?/s)', 'max. PAR (?mol/m?/s)', 'Tlog (degC)', 'OT'
]


class Args(argparse.Namespace):
    def __init__(self, **kwargs):
        defaults = {'patch_len': 16, 'stride': 8, 'output_attention': False, 'n_heads': 8, 'freq': 't'}
        defaults.update(kwargs)
        super().__init__(**defaults)


MODELS_CONFIG: Dict[str, dict] = {
    "gas": {
        "use_model_parallel": False,
        "model_path": "./checkpoints/gas/gas_m1_120_20_30/checkpoint.pth",
        "feature_order": GAS_FEATURE_ORDER,
        "model": 'TimeLLM',
        "task_name": 'long_term_forecast',
        "seq_len": 120,
        "label_len": 20,
        "pred_len": 30,
        "e_layers": 2,
        "d_layers": 1,
        "factor": 1,
        "enc_in": 30,
        "dec_in": 30,
        "c_out": 30,
        "d_model": 32,
        "d_ff": 128,
        "llm_layers": 32,
        "llm_model": 'LLAMA',
        "llm_dim": 4096,
        "data": 'custom',
        "features": 'M',
        "target": 'ch4_face_return_air_0308',
        "embed": 'timeF',
        "root_path": "./dataset/gas/",
        "data_path": "gas_custom.csv",
        "patch_len": 16,
        "prompt_domain": True,
        "prompt_str": "The dataset records gas concentration every minute, comprising 30 indicators representing the gas concentration levels at different monitoring points.",
        "content": "The dataset records gas concentration every minute, comprising 30 indicators representing the gas concentration levels at different monitoring points.",
        "stride": 8,
        "output_attention": False,
        "dropout": 0.1,
        "post_processing": {
            "clip_min": 0.0,      # 瓦斯浓度的物理下限
            "clip_max": 5.0,    # 瓦斯浓度的安全或物理上限 (例如 100%)
            "fill_nan": 0.0       # 如果出现NaN值，用0填充
        },
    },
    # "weather": {
    #     "use_model_parallel": False,
    #     "model_path": "./checkpoints/weather/weather_m10_512_48_96/checkpoint.pth",
    #     "feature_order": WEATHER_FEATURE_ORDER,
    #     "model": 'TimeLLM',
    #     "task_name": 'long_term_forecast',
    #     "seq_len": 512,
    #     "label_len": 48,
    #     "pred_len": 96,
    #     "e_layers": 2,
    #     "d_layers": 1,
    #     "factor": 3,
    #     "enc_in": 21,
    #     "dec_in": 21,
    #     "c_out": 21,
    #     "d_model": 32,
    #     "d_ff": 32,
    #     "llm_layers": 32,
    #     "llm_model": 'LLAMA',
    #     "llm_dim": 4096,
    #     "dropout": 0.1,
    #     "patch_len": 16,
    #     "prompt_domain": True,
    #     "data": 'Weather',
    #     "features": 'M',
    #     "target": 'OT',
    #     "freq": 'h',
    #     "embed": 'timeF',
    #     "root_path": "./dataset/weather/",
    #     "data_path": "weather.csv",
    #     "prompt_str": "Weather is recorded every 10 minutes for the 2020 whole year, which contains 21 meteorological indicators, such as air temperature, humidity, etc.",
    #     "stride": 8,
    #     "output_attention": False,
    #     "activation": "gelu",
    #     "post_processing": {
        #     "clip_min": -50.0,    # 天气温度可能的下限
        #     "clip_max": 60.0,     # 天气温度可能的上限
        #     "fill_nan": 0.0
    #     },
    # },
}

# 4. ---------------- 模型加载与缓存 (无状态) ----------------
# 缓存结构: { model_name: (model, args, content) }
LOADED_MODELS: Dict[str, tuple] = {}
DEVICE = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")


def initialize_model_and_data(model_name: str):
    config_dict = MODELS_CONFIG[model_name]
    args = Args(**config_dict)

    # --- 不再创建 data_buffer ---

    if not hasattr(args, 'content'):
        args.content = load_content(args)

    model_path = config_dict["model_path"]
    if not os.path.exists(model_path): raise FileNotFoundError(f"找不到模型文件: {model_path}")

    model = TimeLLM.Model(args).float()
    model.load_state_dict(torch.load(model_path, map_location=DEVICE))
    model.to(DEVICE)
    model.eval()

    # --- 缓存中不再包含 data_buffer ---
    LOADED_MODELS[model_name] = (model, args, args.content)
    print(f"🎉 模型 '{model_name}' 已成功初始化 (无状态模式)！")


@asynccontextmanager
async def lifespan(app: FastAPI):
    print("服务启动 (无状态模式)... 尝试预加载模型。")
    os.makedirs("./final_models/", exist_ok=True)
    for name in MODELS_CONFIG:
        if os.path.exists(MODELS_CONFIG[name]["model_path"]):
            try:
                initialize_model_and_data(name)
            except Exception as e:
                print(f"警告：初始化模型 '{name}' 失败: {e}")
    yield
    print("服务正在关闭... 清理模型缓存。")
    LOADED_MODELS.clear()


app = FastAPI(title="无状态时序预测 API", version="5.1.0", lifespan=lifespan)


# 5. ---------------- API 输入输出定义 ----------------
class PointData(BaseModel):
    point: str
    values: List[float]


class PredictionInput(BaseModel):
    model: str
    data: List[PointData]


class PredictionOutput(BaseModel):
    model: str
    data: List[PointData]


# 6. ---------------- API 端点实现 (无状态) ----------------
@app.post("/predict", response_model=PredictionOutput, tags=["无状态预测服务"])
async def predict_api(input_data: PredictionInput):
    """
    无状态预测接口：接收完整历史序列，返回独立预测结果
    严格遵循用户约束条件：
    1. 输入全0则输出全0
    2. 输出值不超过clip_max
    3. 输出值保留两位小数
    4. 高灵敏度，不使用缓存数据
    """
    try:
        model_name = input_data.model
        if model_name not in MODELS_CONFIG:
            raise HTTPException(status_code=400, detail=f"不支持的模型: {model_name}")
        
        if model_name not in LOADED_MODELS:
            try:
                initialize_model_and_data(model_name)
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"模型初始化失败: {str(e)}")
        
        model, args, content = LOADED_MODELS[model_name]
        config = MODELS_CONFIG[model_name]
        feature_order = config["feature_order"]
        
        # 验证输入数据
        if len(input_data.data) != len(feature_order):
            raise HTTPException(
                status_code=400, 
                detail=f"输入特征数量不匹配。期望: {len(feature_order)}, 实际: {len(input_data.data)}"
            )
        
        # 验证序列长度
        expected_seq_len = args.seq_len
        for point_data in input_data.data:
            if len(point_data.values) != expected_seq_len:
                raise HTTPException(
                    status_code=400,
                    detail=f"序列长度不匹配。期望: {expected_seq_len}, 实际: {len(point_data.values)}"
                )
        
        # 构建输入数据矩阵 [seq_len, n_features]
        input_matrix = []
        zero_points = []  # 记录全0的点
        
        for i, expected_point in enumerate(feature_order):
            # 找到对应的输入数据
            point_data = None
            for data_point in input_data.data:
                if data_point.point == expected_point:
                    point_data = data_point
                    break
            
            if point_data is None:
                raise HTTPException(
                    status_code=400,
                    detail=f"缺少特征点数据: {expected_point}"
                )
            
            values = point_data.values
            
            # 检查是否全为0（约束条件1）
            if all(v == 0.0 for v in values):
                zero_points.append(i)
            
            input_matrix.append(values)
        
        # 转置矩阵：从 [n_features, seq_len] 到 [seq_len, n_features]
        input_matrix = np.array(input_matrix).T
        
        # 转换为torch tensor
        batch_x = torch.FloatTensor(input_matrix).unsqueeze(0).to(DEVICE)  # [1, seq_len, n_features]
        
        # 创建时间标记（简化版本，使用默认值）
        batch_x_mark = torch.zeros(1, args.seq_len, 4).to(DEVICE)  # [1, seq_len, 4] for month,day,weekday,hour
        
        # 创建decoder输入
        dec_inp = torch.zeros(1, args.label_len + args.pred_len, args.c_out).to(DEVICE)
        batch_y_mark = torch.zeros(1, args.label_len + args.pred_len, 4).to(DEVICE)
        
        # 模型预测
        model.eval()
        with torch.no_grad():
            try:
                outputs = model(batch_x, batch_x_mark, dec_inp, batch_y_mark)
                # outputs shape: [1, pred_len, n_features]
                predictions = outputs.squeeze(0).cpu().numpy()  # [pred_len, n_features]
            except Exception as e:
                raise HTTPException(status_code=500, detail=f"模型预测失败: {str(e)}")
        
        # 应用后处理约束
        post_config = config.get("post_processing", {})
        clip_min = post_config.get("clip_min", 0.0)
        clip_max = post_config.get("clip_max", 5.0)
        fill_nan = post_config.get("fill_nan", 0.0)
        
        # 处理NaN值
        predictions = np.nan_to_num(predictions, nan=fill_nan)
        
        # 应用clip约束（约束条件2）
        predictions = np.clip(predictions, clip_min, clip_max)
        
        # 构建输出结果
        result_data = []
        for i, point_name in enumerate(feature_order):
            point_predictions = predictions[:, i].tolist()
            
            # 约束条件1：如果输入全0，输出也全0
            if i in zero_points:
                point_predictions = [0.0] * len(point_predictions)
            
            # 约束条件3：四舍五入保留两位小数
            point_predictions = [
                float(Decimal(str(val)).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP))
                for val in point_predictions
            ]
            
            result_data.append(PointData(
                point=point_name,
                values=point_predictions
            ))
        
        return PredictionOutput(
            model=model_name,
            data=result_data
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"预测过程中发生错误: {str(e)}")


# --- 主函数 ---
if __name__ == '__main__':
    uvicorn.run("api_local_claude:app", host="0.0.0.0", port=8000, reload=True)
