# file: api_local_grok.py (优化后的无状态预测版)

# 1. 导入必要的库
import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
import numpy as np
import torch
import os
import argparse
import logging
from typing import Dict, List, Optional
from contextlib import asynccontextmanager
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_HALF_UP

from models import TimeLLM
from utils.tools import load_content, time_features  # 确保导入 time_features

# 2. 配置日志
logging.basicConfig(
    level=logging.INFO,
    filename="predict_api.log",
    format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 3. 定义 FastAPI 应用
app = FastAPI(
    title="优化无状态时序预测 API",
    description="接收30个监测点的120个历史瓦斯浓度数据，返回未来30个时间步的预测结果。支持批量预测和动态模型加载。",
    version="5.2.0"
)

# 4. 模型配置与特征顺序
GAS_FEATURE_ORDER = [
    '66081101194701MN0001001A090', '66081101194701MN0001002A010', '66081101194701MN0001002A040',
    '66081101194701MN0001003A010', '66081101194701MN0001003A020', '66081101194701MN0001003A030',
    '66081101194701MN0001003A050', '66081101194701MN0001005A080', '66081101194701MN0001005A090',
    '66081101194701MN0001005A130', '66081101194701MN0001008A150', '66081101194701MN0001009A070',
    '66081101194701MN0001009A130', '66081101194701MN0001010A010', '66081101194701MN0001010A090',
    '66081101194701MN0001012A010', '66081101194701MN0001012A050', '66081101194701MN0001012A110',
    '66081101194701MN0001012A130', '66081101194701MN0001013A090', '66081101194701MN0001015A090',
    '66081101194701MN0001015A130', '66081101194701MN0001017A110', '66081101194701MN0001017A160',
    '66081101194701MN0001018A090', '66081101194701MN0001018A100', '66081101194701MN0001018A130',
    '66081101194701MN0001021A080', '66081101194701MN0001023A090', '66081101194701MN0001023A130'
]


class Args(argparse.Namespace):
    def __init__(self, **kwargs):
        defaults = {'patch_len': 16, 'stride': 8, 'output_attention': False, 'n_heads': 8, 'freq': 't'}
        defaults.update(kwargs)
        super().__init__(**defaults)


MODELS_CONFIG: Dict[str, dict] = {
    "gas": {
        "use_model_parallel": False,
        "model_path": "./checkpoints/gas/gas_m1_120_20_30/checkpoint.pth",
        "feature_order": GAS_FEATURE_ORDER,
        "model": 'TimeLLM',
        "task_name": 'long_term_forecast',
        "seq_len": 120,
        "label_len": 20,
        "pred_len": 30,
        "e_layers": 2,
        "d_layers": 1,
        "factor": 1,
        "enc_in": 30,
        "dec_in": 30,
        "c_out": 30,
        "d_model": 32,
        "d_ff": 128,
        "llm_layers": 32,
        "llm_model": 'LLAMA',
        "llm_dim": 4096,
        "data": 'custom',
        "features": 'M',
        "target": '66081101194701MN0001001A090',
        "embed": 'timeF',
        "root_path": "./dataset/gas/",
        "data_path": "gas_custom.csv",
        "patch_len": 16,
        "prompt_domain": 1,
        "prompt_str": "The dataset records gas concentration every minute, comprising 30 indicators representing the gas concentration levels at different monitoring points.",
        "content": "The dataset records gas concentration every minute, comprising 30 indicators representing the gas concentration levels at different monitoring points.",
        "stride": 8,
        "output_attention": False,
        "dropout": 0.1,
        "n_heads": 8,
        "freq": 't',
        "post_processing": {
            "clip_min": 0.0,
            "clip_max": 5.0,
            "fill_nan": 0.0
        },
    },
}

# 5. 模型加载与缓存
LOADED_MODELS: Dict[str, tuple] = {}
DEVICE = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")


def initialize_model_and_data(model_name: str):
    config_dict = MODELS_CONFIG.get(model_name)
    if not config_dict:
        raise ValueError(f"模型配置 '{model_name}' 不存在")

    args = Args(**config_dict)
    if not hasattr(args, 'content') or args.content is None:
        try:
            args.content = load_content(args)
        except Exception as e:
            logger.warning(f"无法加载content，使用默认值: {e}")
            args.content = config_dict.get("content", "")

    model_path = config_dict["model_path"]
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"找不到模型文件: {model_path}")

    model = TimeLLM.Model(args).float()
    model.load_state_dict(torch.load(model_path, map_location=DEVICE))
    model.to(DEVICE)
    model.eval()

    LOADED_MODELS[model_name] = (model, args, args.content)
    logger.info(f"模型 '{model_name}' 已成功初始化 (无状态模式)")


@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("服务启动 (无状态模式)... 尝试预加载模型")
    os.makedirs("./final_models/", exist_ok=True)
    for name in MODELS_CONFIG:
        if os.path.exists(MODELS_CONFIG[name]["model_path"]):
            try:
                initialize_model_and_data(name)
            except Exception as e:
                logger.error(f"初始化模型 '{name}' 失败: {e}")
    yield
    logger.info("服务正在关闭... 清理模型缓存")
    LOADED_MODELS.clear()


app = FastAPI(title="优化无状态时序预测 API", version="5.2.0", lifespan=lifespan)


# 6. API 输入输出定义
class PointData(BaseModel):
    point: str = Field(..., description="监测点名称")
    values: List[float] = Field(..., description="120个历史瓦斯浓度值")
    timestamp: Optional[str] = Field(None, description="最后一个时间步的时间戳，格式: YYYY-MM-DD HH:MM:SS")


class PredictionInput(BaseModel):
    model: str = Field(..., description="模型名称，例如 'gas'")
    data: List[PointData] = Field(..., description="30个监测点的历史数据")
    post_processing: Optional[dict] = Field(None, description="可选后处理参数，覆盖默认配置")


class PredictionOutput(BaseModel):
    model: str = Field(..., description="使用的模型名称")
    data: List[PointData] = Field(..., description="预测的30个监测点的未来30个时间步数据")


# 7. API 端点实现
@app.post(
    "/predict",
    response_model=PredictionOutput,
    tags=["无状态预测服务"],
    summary="瓦斯浓度预测",
    description="输入30个监测点的120个历史瓦斯浓度值（范围0.0-5.0），预测未来30个时间步的浓度值。支持批量预测和时间特征。"
)
async def predict_api(input_data: PredictionInput):
    model_name = input_data.model
    if model_name not in LOADED_MODELS:
        raise HTTPException(status_code=404, detail=f"模型 '{model_name}' 未加载或初始化失败")

    config = MODELS_CONFIG.get(model_name)
    if not config:
        raise HTTPException(status_code=400, detail=f"不支持的模型: {model_name}")

    model, args, content = LOADED_MODELS[model_name]
    feature_order = config["feature_order"]

    try:
        # 解析输入数据
        input_dict = {item.point: item.values for item in input_data.data}
        if len(input_dict) != args.enc_in:
            raise ValueError(f"需要 {args.enc_in} 个监测点，收到了 {len(input_dict)} 个")

        # 验证数据长度和值范围
        for point, values in input_dict.items():
            if len(values) != args.seq_len:
                raise ValueError(f"监测点 '{point}' 需要 {args.seq_len} 个时间步，收到了 {len(values)} 个")
            if any(v < 0.0 or v > 5.0 for v in values):
                raise ValueError(f"监测点 '{point}' 的值超出范围 [0.0, 5.0]")

        # 按照特征顺序排列数据
        ordered_sequences = [input_dict[name] for name in feature_order]
        input_array = np.array(ordered_sequences).T  # Shape: [seq_len, enc_in]

        # 生成时间标记
        time_mark_dim = 4  # freq='t' 对应 4 个时间特征
        batch_x_mark = torch.zeros((1, args.seq_len, time_mark_dim), device=DEVICE).float()
        batch_y_mark = torch.zeros((1, args.label_len + args.pred_len, time_mark_dim), device=DEVICE).float()

        # 如果提供了时间戳，生成时间特征
        timestamps = [item.timestamp for item in input_data.data if item.timestamp]
        if timestamps and len(timestamps) == len(input_data.data):
            try:
                last_timestamp = datetime.strptime(timestamps[0], "%Y-%m-%d %H:%M:%S")
                time_seq = [last_timestamp - timedelta(minutes=args.seq_len - i - 1) for i in range(args.seq_len)]
                time_seq_y = [last_timestamp - timedelta(minutes=args.label_len - i - 1) for i in
                              range(args.label_len)] + \
                             [last_timestamp + timedelta(minutes=i) for i in range(args.pred_len)]
                batch_x_mark = time_features(time_seq, freq=args.freq).unsqueeze(0).to(DEVICE)
                batch_y_mark = time_features(time_seq_y, freq=args.freq).unsqueeze(0).to(DEVICE)
            except Exception as e:
                logger.warning(f"时间戳解析失败，使用默认零标记: {e}")

        # 准备模型输入
        batch_x = torch.from_numpy(input_array).float().unsqueeze(0).to(DEVICE)  # Shape: [1, seq_len, enc_in]

        # 构造解码器输入
        dec_inp = torch.zeros((1, args.pred_len, args.c_out), device=DEVICE).float()
        dec_inp = torch.cat([batch_x[:, -args.label_len:, :], dec_inp], dim=1).float()

        # 模型推理
        with torch.no_grad():
            outputs = model(batch_x, batch_x_mark, dec_inp, batch_y_mark)

        # 获取预测结果
        prediction_array = outputs.detach().cpu().numpy()[0]  # Shape: [pred_len, c_out]

        # 应用后处理规则
        pp_rules = input_data.post_processing or config.get("post_processing", {})
        min_val = pp_rules.get("clip_min", 0.0)
        max_val = pp_rules.get("clip_max", 5.0)
        fill_val = pp_rules.get("fill_nan", 0.0)

        if fill_val is not None:
            prediction_array[np.isnan(prediction_array)] = fill_val
        if min_val is not None and max_val is not None:
            np.clip(prediction_array, min_val, max_val, out=prediction_array)

        # 转置预测结果
        prediction_array_transposed = prediction_array.T  # Shape: [c_out, pred_len]

        # 记录输入全为零的监测点
        zero_input_points = {item.point for item in input_data.data if all(v == 0 for v in item.values)}

        # 构造输出数据
        output_data = []
        pred_len = config.get("pred_len", args.pred_len)
        for i, feature_name in enumerate(feature_order):
            if feature_name in zero_input_points:
                final_values = [0.0] * pred_len
            else:
                final_values = [
                    float(Decimal(str(val)).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP))
                    for val in prediction_array_transposed[i]
                ]
            output_data.append(PointData(point=feature_name, values=final_values))

        logger.info(f"预测完成，模型: {model_name}, 输入数据点数: {len(input_data.data)}")
        return PredictionOutput(model=model_name, data=output_data)

    except Exception as e:
        logger.error(f"预测失败: {str(e)}, 输入数据摘要: {str(input_data.dict())[:200]}")
        raise HTTPException(status_code=500, detail=f"预测过程中发生内部错误: {str(e)}")


@app.post(
    "/load_model/{model_name}",
    summary="动态加载模型",
    description="加载指定模型到内存，支持动态更新模型配置。"
)
async def load_model(model_name: str):
    if model_name not in MODELS_CONFIG:
        raise HTTPException(status_code=400, detail=f"不支持的模型: {model_name}")
    try:
        initialize_model_and_data(model_name)
        logger.info(f"模型 '{model_name}' 动态加载成功")
        return {"status": f"模型 '{model_name}' 已加载"}
    except Exception as e:
        logger.error(f"动态加载模型 '{model_name}' 失败: {e}")
        raise HTTPException(status_code=500, detail=f"加载模型失败: {str(e)}")


# 8. 主函数
if __name__ == '__main__':
    uvicorn.run("api_local_grok:app", host="0.0.0.0", port=8000, reload=True)