# file: api_local.py (最终无状态预测版 - 已修复 content 未定义 BUG)

# 1. 导入必要的库
import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
import numpy as np
import torch
import os
import argparse
from typing import Dict, List
from collections import deque
from contextlib import asynccontextmanager

from models import TimeLLM
from utils.tools import load_content  # 确保 load_content 被导入
from decimal import Decimal, ROUND_HALF_UP
# 2. 定义 FastAPI 应用
app = FastAPI(
    title="无状态时序预测 API",
    description="每次调用接收完整的历史序列，返回独立的预测结果。",
    version="5.1.0"  # 修复版本
)

# 3. ---------------- 模型配置与特征顺序 (保持不变) ----------------
GAS_FEATURE_ORDER = [
    '66081101194701MN0001001A090', '66081101194701MN0001002A010', '66081101194701MN0001002A040',
    '66081101194701MN0001003A010', '66081101194701MN0001003A020', '66081101194701MN0001003A030',
    '66081101194701MN0001003A050', '66081101194701MN0001005A080', '66081101194701MN0001005A090',
    '66081101194701MN0001005A130', '66081101194701MN0001008A150', '66081101194701MN0001009A070',
    '66081101194701MN0001009A130', '66081101194701MN0001010A010', '66081101194701MN0001010A090',
    '66081101194701MN0001012A010', '66081101194701MN0001012A050', '66081101194701MN0001012A110',
    '66081101194701MN0001012A130', '66081101194701MN0001013A090', '66081101194701MN0001015A090',
    '66081101194701MN0001015A130', '66081101194701MN0001017A110', '66081101194701MN0001017A160',
    '66081101194701MN0001018A090', '66081101194701MN0001018A100', '66081101194701MN0001018A130',
    '66081101194701MN0001021A080', '66081101194701MN0001023A090', '66081101194701MN0001023A130'
]
WEATHER_FEATURE_ORDER = [
    'p (mbar)', 'T (degC)', 'Tpot (K)', 'Tdew (degC)', 'rh (%)', 'VPmax (mbar)', 'VPact (mbar)', 'VPdef (mbar)',
    'sh (g/kg)', 'H2OC (mmol/mol)', 'rho (g/m**3)', 'wv (m/s)', 'max. wv (m/s)', 'wd (deg)', 'rain (mm)', 'raining (s)',
    'SWDR (W/m?)', 'PAR (?mol/m?/s)', 'max. PAR (?mol/m?/s)', 'Tlog (degC)', 'OT'
]


class Args(argparse.Namespace):
    def __init__(self, **kwargs):
        defaults = {'patch_len': 16, 'stride': 8, 'output_attention': False, 'n_heads': 8, 'freq': 't'}
        defaults.update(kwargs)
        super().__init__(**defaults)


MODELS_CONFIG: Dict[str, dict] = {
    "gas": {
        "use_model_parallel": False,
        "model_path": "./checkpoints/gas/gas_m1_120_20_30/checkpoint.pth",
        "feature_order": GAS_FEATURE_ORDER,
        "model": 'TimeLLM',
        "task_name": 'long_term_forecast',
        "seq_len": 120,
        "label_len": 20,
        "pred_len": 30,
        "e_layers": 2,
        "d_layers": 1,
        "factor": 1,
        "enc_in": 30,
        "dec_in": 30,
        "c_out": 30,
        "d_model": 32,
        "d_ff": 128,
        "llm_layers": 32,
        "llm_model": 'LLAMA',
        "llm_dim": 4096,
        "data": 'custom',
        "features": 'M',
        "target": '66081101194701MN0001001A090',
        "embed": 'timeF',
        "root_path": "./dataset/gas/",
        "data_path": "gas_custom.csv",
        "patch_len": 16,
        "prompt_domain": True,
        "prompt_str": "The dataset records gas concentration every minute, comprising 30 indicators representing the gas concentration levels at different monitoring points.",
        "content": "The dataset records gas concentration every minute, comprising 30 indicators representing the gas concentration levels at different monitoring points.",
        "stride": 8,
        "output_attention": False,
        "dropout": 0.1,
        "post_processing": {
            "clip_min": 0.0,      # 瓦斯浓度的物理下限
            "clip_max": 5.0,    # 瓦斯浓度的安全或物理上限 (例如 100%)
            "fill_nan": 0.0       # 如果出现NaN值，用0填充
        },
    },
    # "weather": {
    #     "use_model_parallel": False,
    #     "model_path": "./checkpoints/weather/weather_m10_512_48_96/checkpoint.pth",
    #     "feature_order": WEATHER_FEATURE_ORDER,
    #     "model": 'TimeLLM',
    #     "task_name": 'long_term_forecast',
    #     "seq_len": 512,
    #     "label_len": 48,
    #     "pred_len": 96,
    #     "e_layers": 2,
    #     "d_layers": 1,
    #     "factor": 3,
    #     "enc_in": 21,
    #     "dec_in": 21,
    #     "c_out": 21,
    #     "d_model": 32,
    #     "d_ff": 32,
    #     "llm_layers": 32,
    #     "llm_model": 'LLAMA',
    #     "llm_dim": 4096,
    #     "dropout": 0.1,
    #     "patch_len": 16,
    #     "prompt_domain": True,
    #     "data": 'Weather',
    #     "features": 'M',
    #     "target": 'OT',
    #     "freq": 'h',
    #     "embed": 'timeF',
    #     "root_path": "./dataset/weather/",
    #     "data_path": "weather.csv",
    #     "prompt_str": "Weather is recorded every 10 minutes for the 2020 whole year, which contains 21 meteorological indicators, such as air temperature, humidity, etc.",
    #     "stride": 8,
    #     "output_attention": False,
    #     "activation": "gelu",
    #     "post_processing": {
    #         "clip_min": -50.0,    # 天气温度可能的下限
    #         "clip_max": 60.0,     # 天气温度可能的上限
    #         "fill_nan": 0.0
    #     },
    # },
}

# 4. ---------------- 模型加载与缓存 (无状态) ----------------
# 缓存结构: { model_name: (model, args, content) }
LOADED_MODELS: Dict[str, tuple] = {}
DEVICE = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")


def initialize_model_and_data(model_name: str):
    config_dict = MODELS_CONFIG[model_name]
    args = Args(**config_dict)

    if not hasattr(args, 'content'):
        args.content = load_content(args)

    model_path = config_dict["model_path"]
    if not os.path.exists(model_path): raise FileNotFoundError(f"找不到模型文件: {model_path}")

    model = TimeLLM.Model(args).float()
    model.load_state_dict(torch.load(model_path, map_location=DEVICE))
    model.to(DEVICE)
    model.eval()

    # --- 缓存中不再包含 data_buffer ---
    LOADED_MODELS[model_name] = (model, args, args.content)
    print(f"🎉 模型 '{model_name}' 已成功初始化 (无状态模式)！")


@asynccontextmanager
async def lifespan(app: FastAPI):
    print("服务启动 (无状态模式)... 尝试预加载模型。")
    os.makedirs("./final_models/", exist_ok=True)
    for name in MODELS_CONFIG:
        if os.path.exists(MODELS_CONFIG[name]["model_path"]):
            try:
                initialize_model_and_data(name)
            except Exception as e:
                print(f"警告：初始化模型 '{name}' 失败: {e}")
    yield
    print("服务正在关闭... 清理模型缓存。")
    LOADED_MODELS.clear()


app = FastAPI(title="无状态时序预测 API", version="5.1.0", lifespan=lifespan)


# 5. ---------------- API 输入输出定义 ----------------
class PointData(BaseModel):
    point: str
    values: List[float]


class PredictionInput(BaseModel):
    model: str
    data: List[PointData]


class PredictionOutput(BaseModel):
    model: str
    data: List[PointData]


# 6. ---------------- API 端点实现 (无状态) ----------------

@app.get("/health", tags=["健康检查"])
async def health_check():
    """
    健康检查端点
    """
    return {
        "status": "healthy",
        "message": "瓦斯浓度预测API运行正常",
        "loaded_models": list(LOADED_MODELS.keys()),
        "device": str(DEVICE)
    }

@app.get("/", tags=["根路径"])
async def root():
    """
    根路径，返回API信息
    """
    return {
        "message": "瓦斯浓度预测API",
        "version": "5.1.0",
        "docs": "/docs",
        "health": "/health"
    }
@app.post("/predict", response_model=PredictionOutput, tags=["无状态预测服务"])
async def predict_api(input_data: PredictionInput):
    """
    无状态瓦斯浓度预测接口
    """
    try:
        model_name = input_data.model
        if model_name not in MODELS_CONFIG:
            raise HTTPException(status_code=400, detail=f"不支持的模型: {model_name}")
        
        # 确保模型已加载
        if model_name not in LOADED_MODELS:
            initialize_model_and_data(model_name)
        
        model, args, content = LOADED_MODELS[model_name]
        config = MODELS_CONFIG[model_name]
        feature_order = config["feature_order"]
        
        # 验证输入数据
        if len(input_data.data) != len(feature_order):
            raise HTTPException(
                status_code=400, 
                detail=f"输入数据点数量不匹配，期望 {len(feature_order)} 个，实际 {len(input_data.data)} 个"
            )
        
        # 检查每个point的values长度
        expected_seq_len = args.seq_len
        for point_data in input_data.data:
            if len(point_data.values) != expected_seq_len:
                raise HTTPException(
                    status_code=400,
                    detail=f"监测点 {point_data.point} 的历史数据长度不匹配，期望 {expected_seq_len} 个，实际 {len(point_data.values)} 个"
                )
        
        # 构建输入数据矩阵 (seq_len, num_features)
        input_matrix = np.zeros((expected_seq_len, len(feature_order)))
        point_to_index = {point: idx for idx, point in enumerate(feature_order)}
        zero_points = set()  # 记录全为0的监测点
        
        for point_data in input_data.data:
            if point_data.point not in point_to_index:
                raise HTTPException(
                    status_code=400,
                    detail=f"未知的监测点: {point_data.point}"
                )
            
            feature_idx = point_to_index[point_data.point]
            values = np.array(point_data.values)
            
            # 检查是否全为0
            if np.all(values == 0):
                zero_points.add(point_data.point)
            
            input_matrix[:, feature_idx] = values
        
        # 转换为tensor并添加batch维度
        input_tensor = torch.tensor(input_matrix, dtype=torch.float32).unsqueeze(0).to(DEVICE)
        
        # 创建时间特征（简化处理，使用零向量）
        batch_x_mark = torch.zeros(1, expected_seq_len, 4).to(DEVICE)  # 假设时间特征维度为4
        
        # 创建decoder输入
        dec_inp = torch.zeros(1, args.label_len + args.pred_len, len(feature_order)).to(DEVICE)
        dec_inp[:, :args.label_len, :] = input_tensor[:, -args.label_len:, :]
        
        # decoder时间特征
        batch_y_mark = torch.zeros(1, args.label_len + args.pred_len, 4).to(DEVICE)
        
        # 模型预测
        model.eval()
        with torch.no_grad():
            if args.output_attention:
                outputs = model(input_tensor, batch_x_mark, dec_inp, batch_y_mark)[0]
            else:
                outputs = model(input_tensor, batch_x_mark, dec_inp, batch_y_mark)
        
        # 提取预测结果 (batch_size, pred_len, num_features)
        predictions = outputs[:, -args.pred_len:, :].cpu().numpy()
        predictions = predictions[0]  # 移除batch维度 (pred_len, num_features)
        
        # 后处理
        post_config = config.get("post_processing", {})
        clip_min = post_config.get("clip_min", 0.0)
        clip_max = post_config.get("clip_max", 5.0)
        fill_nan = post_config.get("fill_nan", 0.0)
        
        # 处理NaN值
        predictions = np.nan_to_num(predictions, nan=fill_nan)
        
        # 应用clip限制
        predictions = np.clip(predictions, clip_min, clip_max)
        
        # 构建响应数据
        response_data = []
        for idx, point in enumerate(feature_order):
            point_predictions = predictions[:, idx].tolist()
            
            # 如果输入全为0，输出也设为0
            if point in zero_points:
                point_predictions = [0.0] * len(point_predictions)
            else:
                # 四舍五入保留两位小数
                point_predictions = [round(float(val), 2) for val in point_predictions]
            
            response_data.append(PointData(
                point=point,
                values=point_predictions
            ))
        
        return PredictionOutput(
            model=model_name,
            data=response_data
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"预测失败: {str(e)}")


# --- 主函数 ---
if __name__ == '__main__':
    uvicorn.run("api_local_kiro:app", host="0.0.0.0", port=8000, reload=True)