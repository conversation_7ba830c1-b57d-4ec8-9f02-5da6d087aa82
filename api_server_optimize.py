# file: api_server.py
# 最终现代化版: 严格基于用户提供的v8.0.0版本，仅将 on_event 迁移到 lifespan。

# 1. 导入必要的库
import uvicorn
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
import numpy as np
import torch
import os
import argparse
from typing import Dict, List
from collections import deque
from contextlib import asynccontextmanager # <-- 添加了这行 import

from models import TimeLLM
from utils.tools import load_content

from collections import OrderedDict

# (前面所有的 MODELS_CONFIG, Args, GAS_FEATURE_ORDER 等定义都保持原样)
# ...
GAS_FEATURE_ORDER = [
    'ch4_face_return_air_0308', 'ch4_upper_corner_0308', 'ch4_return_bunker_0308', 'ch4_face_0308', 'ch4_transport_bunker_0308', 'sprayer_bottom_shaft', 'ch4_refuge_chamber_inside', 'ch4_ground_bunker_se', 'ch4_ground_belt_west', 'ch4_ground_bunker_nw', 'ch4_ground_belt_east', 'ch4_return_bunker_0307', 'ch4_transport_face_0305', 'ch4_transport_return_air_0305', 'ch4_rail_belt_face_5', 'ch4_rail_belt_return_air_5', 'ch4_main_return_air', 'ch4_central_return_air', 'ch4_ground_pump_1_2', 'ch4_ground_pump_3_4', 'ch4_ground_pump_pipe', 'ch4_pump_room_electric', 'ch4_return_bunker_0305', 'ch4_return_face_0305', 'ch4_return_return_air_0305', 'ch4_refuge_chamber_exit', 'ch4_main_inclined_bunker', 'ch4_central_return_face', 'ch4_central_return_dev_air'
]
WEATHER_FEATURE_ORDER = [
    'p (mbar)', 'T (degC)', 'Tpot (K)', 'Tdew (degC)', 'rh (%)', 'VPmax (mbar)', 'VPact (mbar)', 'VPdef (mbar)', 'sh (g/kg)', 'H2OC (mmol/mol)', 'rho (g/m**3)', 'wv (m/s)', 'max. wv (m/s)', 'wd (deg)', 'rain (mm)', 'raining (s)', 'SWDR (W/m?)', 'PAR (?mol/m?/s)', 'max. PAR (?mol/m?/s)', 'Tlog (degC)', 'OT'
]
class Args(argparse.Namespace):
    def __init__(self, **kwargs):
        defaults = {'patch_len': 16, 'stride': 8, 'output_attention': False, 'n_heads': 8}
        defaults.update(kwargs)
        super().__init__(**defaults)

MODELS_CONFIG: Dict[str, dict] = {
    "gas": {
        "use_model_parallel": False,
        "model_path": "./checkpoints/gas/gas_m1_120_20_30/checkpoint.pth",
        "feature_order": GAS_FEATURE_ORDER,
        "model": 'TimeLLM',
        "task_name": 'long_term_forecast',
        "seq_len": 120,
        "label_len": 20,
        "pred_len": 30,
        "e_layers": 2,
        "d_layers": 1,
        "factor": 1,
        "enc_in": 30,
        "dec_in": 30,
        "c_out": 30,
        "d_model": 32,
        "d_ff": 128,
        "llm_layers": 32,
        "llm_model": 'LLAMA',
        "llm_dim": 4096,
        "data": 'custom',
        "features": 'M',
        "target": 'ch4_face_return_air_0308',
        "embed": 'timeF',
        "root_path": "./dataset/gas/",
        "data_path": "gas_custom.csv",
        "patch_len": 16,
        "prompt_domain": True,
        "prompt_str": "The dataset records gas concentration every minute, comprising 30 indicators representing the gas concentration levels at different monitoring points.",
        "content": "The dataset records gas concentration every minute, comprising 30 indicators representing the gas concentration levels at different monitoring points.",
        "stride": 8,
        "output_attention": False,
        "dropout": 0.1,
        "post_processing": {
            "clip_min": 0.0,      # 瓦斯浓度的物理下限
            "clip_max": 1500.0,    # 瓦斯浓度的安全或物理上限 (例如 100%)
            "fill_nan": 0.0       # 如果出现NaN值，用0填充
        },
    },
    # "weather": {
    #     "use_model_parallel": False,
    #     "model_path": "./checkpoints/weather/weather_m10_512_48_96/checkpoint.pth",
    #     "feature_order": WEATHER_FEATURE_ORDER,
    #     "model": 'TimeLLM',
    #     "task_name": 'long_term_forecast',
    #     "seq_len": 512,
    #     "label_len": 48,
    #     "pred_len": 96,
    #     "e_layers": 2,
    #     "d_layers": 1,
    #     "factor": 3,
    #     "enc_in": 21,
    #     "dec_in": 21,
    #     "c_out": 21,
    #     "d_model": 32,
    #     "d_ff": 32,
    #     "llm_layers": 32,
    #     "llm_model": 'LLAMA',
    #     "llm_dim": 4096,
    #     "dropout": 0.1,
    #     "patch_len": 16,
    #     "prompt_domain": True,
    #     "data": 'Weather',
    #     "features": 'M',
    #     "target": 'OT',
    #     "freq": 'h',
    #     "embed": 'timeF',
    #     "root_path": "./dataset/weather/",
    #     "data_path": "weather.csv",
    #     "prompt_str": "Weather is recorded every 10 minutes for the 2020 whole year, which contains 21 meteorological indicators, such as air temperature, humidity, etc.",
    #     "stride": 8,
    #     "output_attention": False,
    #     "activation": "gelu",
    #     "post_processing": {
        #     "clip_min": -50.0,    # 天气温度可能的下限
        #     "clip_max": 60.0,     # 天气温度可能的上限
        #     "fill_nan": 0.0
    #     },
    # },
}


# 使用有序字典作为LRU缓存的基础
# 结构: { model_name: (model, args, content, data_buffer) }
LOADED_MODELS_CACHE = OrderedDict()
# 设置缓存的最大容量，例如，显存中最多同时保留2个模型
MAX_CACHE_SIZE = 2
DEVICE = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")


def unload_model(model_name: str):
    """从缓存中卸载一个模型并释放显存"""
    if model_name in LOADED_MODELS_CACHE:
        print(f"--- 正在卸载模型 '{model_name}' 以释放显存... ---")
        # 从字典中删除模型相关的所有内容
        del LOADED_MODELS_CACHE[model_name]
        # 强制进行垃圾回收并清空PyTorch缓存
        import gc
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        print(f"--- 模型 '{model_name}' 已卸载。 ---")


def manage_and_get_model(model_name: str):
    """
    智能模型调度器：
    1. 按需加载 (Lazy Loading)
    2. LRU缓存管理 (Cache with LRU policy)
    3. 自动卸载 (Automatic Unloading)
    """
    # 1. 检查模型是否已在缓存中
    if model_name in LOADED_MODELS_CACHE:
        print(f"--- 模型 '{model_name}' 命中缓存。将其更新为最新使用。 ---")
        LOADED_MODELS_CACHE.move_to_end(model_name)  # 更新其在有序字典中的位置
        return LOADED_MODELS_CACHE[model_name]

    # 2. 如果缓存已满，卸载最久未使用的模型
    if len(LOADED_MODELS_CACHE) >= MAX_CACHE_SIZE:
        # OrderedDict 的 popitem(last=False) 会移除并返回第一个插入的（最旧的）项
        oldest_model_name, _ = LOADED_MODELS_CACHE.popitem(last=False)
        print(f"--- 缓存已满 ({MAX_CACHE_SIZE})。开始卸载最久未使用的模型: '{oldest_model_name}' ---")
        # (popitem 已经从字典移除了，我们只需要清理显存)
        import gc
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

    # 3. 加载新模型
    print(f"--- 模型 '{model_name}' 未在缓存中，正在从磁盘按需加载... ---")
    if model_name not in MODELS_CONFIG:
        # 为了健壮性，这里应该抛出HTTP异常
        raise HTTPException(status_code=404, detail=f"模型 '{model_name}' 的配置未找到。")

    config_dict = MODELS_CONFIG[model_name]
    args = Args(**config_dict)
    model_path = config_dict["model_path"]
    if not os.path.exists(model_path):
        raise HTTPException(status_code=404, detail=f"找不到模型文件: {model_path}")

    # 这里的加载逻辑，就是您原来 initialize_model_and_data 函数的核心
    model = TimeLLM.Model(args).float()
    model.load_state_dict(torch.load(model_path, map_location=DEVICE))
    model.to(DEVICE)
    model.eval()
    content = load_content(args)
    data_buffer = np.zeros((args.seq_len, args.enc_in))

    # 将新加载的模型放入缓存
    LOADED_MODELS_CACHE[model_name] = (model, args, content, data_buffer)
    print(f"🎉 模型 '{model_name}' 加载成功并已缓存！当前缓存大小: {len(LOADED_MODELS_CACHE)}/{MAX_CACHE_SIZE}")

    return LOADED_MODELS_CACHE[model_name]

# ======================= 核心修改区域 =======================
@asynccontextmanager
async def lifespan(app: FastAPI):
    # --- 服务启动时不再预加载任何模型 ---
    print("服务启动 (动态加载模式)... 等待API请求。")
    # 确保目录存在即可
    os.makedirs("./checkpoints/gas/", exist_ok=True)
    os.makedirs("./checkpoints/weather/", exist_ok=True)

    yield

    # --- 服务关闭时清理所有缓存 ---
    print("服务正在关闭... 清理所有模型缓存。")
    # 使用 list() 来避免在迭代时修改字典
    for model_name in list(LOADED_MODELS_CACHE.keys()):
        unload_model(model_name)

# 2. 创建 FastAPI 应用实例，并传入 lifespan
app = FastAPI(
    title="智能时序预测 API (最终版)",
    description="接收结构化的、部分最新的数据，服务器自动维护历史序列并返回结构化的预测结果。",
    version="8.0.0",
    lifespan=lifespan # <-- 添加这个参数
)

# 3. 旧的 @app.on_event("startup") 已被上面的 lifespan 完全替代，无需保留
# ==========================================================

# (后续所有 API 定义和端点实现代码，完全保持您提供的版本不变)
class PointData(BaseModel):
    point: str = Field(..., description="监测点名称")
    values: List[float] = Field(..., description="该监测点的一段最新序列数据")

class PredictionInput(BaseModel):
    model: str = Field(..., example="gas")
    data: List[PointData]

class PredictionOutput(BaseModel):
    model: str
    data: List[PointData]

@app.post("/predict", response_model=PredictionOutput, tags=["智能更新预测服务"])
async def predict_api(input_data: PredictionInput):
    model_name = input_data.model
    try:
        # 调用新的智能调度器来获取模型
        model, args, content, data_buffer = manage_and_get_model(model_name)
    except Exception as e:
        # 直接抛出调度器或加载过程中可能发生的任何错误
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(status_code=500, detail=f"处理模型 '{model_name}' 时发生严重错误: {str(e)}")

    config = MODELS_CONFIG[model_name]
    feature_order = MODELS_CONFIG[model_name]["feature_order"]

    try:
        input_dict = {item.point: item.values for item in input_data.data}
        if len(input_dict) != args.enc_in:
            raise ValueError(f"需要 {args.enc_in} 个监测点，收到了 {len(input_dict)} 个。")
        ordered_sequences = [input_dict[name] for name in feature_order]
        new_data_array = np.array(ordered_sequences).T
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"解析输入数据时出错: {e}")

    num_new_points = new_data_array.shape[0]
    if num_new_points >= args.seq_len:
        data_buffer[:] = new_data_array[-args.seq_len:]
    else:
        data_buffer[:-num_new_points] = data_buffer[num_new_points:]
        data_buffer[-num_new_points:] = new_data_array

    try:
        # 对于并行模型，输入数据应在CPU上
        device_for_input = "cpu" if config.get("use_model_parallel") else DEVICE
        batch_x = torch.from_numpy(data_buffer).float().unsqueeze(0).to(device_for_input)

        time_mark_dim = 4
        batch_x_mark = torch.zeros((1, args.seq_len, time_mark_dim), device=DEVICE).float()
        batch_y_mark = torch.zeros((1, args.label_len + args.pred_len, time_mark_dim), device=DEVICE).float()
        dec_inp = torch.zeros((1, args.pred_len, args.c_out), device=DEVICE).float()
        dec_inp = torch.cat([batch_x[:, -args.label_len:, :], dec_inp], dim=1).float()

        with torch.no_grad():
            outputs = model(batch_x, batch_x_mark, dec_inp, batch_y_mark, content=content)

        prediction_array = outputs.detach().cpu().numpy()[0]  # Shape: [pred_len, c_out]

        # ======================= 关键修复：动态应用后处理规则 =======================
        pp_rules = config.get("post_processing")
        if pp_rules:
            min_val = pp_rules.get("clip_min")
            max_val = pp_rules.get("clip_max")
            fill_val = pp_rules.get("fill_nan")

            print(f"应用后处理规则: min={min_val}, max={max_val}, fill_nan={fill_val}")

            if fill_val is not None:
                prediction_array[np.isnan(prediction_array)] = fill_val

            np.clip(prediction_array, min_val, max_val, out=prediction_array)
        # =========================================================================

        prediction_array_transposed = prediction_array.T
        output_data = [
            PointData(point=feature_name, values=prediction_array_transposed[i].tolist())
            for i, feature_name in enumerate(feature_order)
        ]
        return PredictionOutput(model=model_name, data=output_data)

    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"预测过程中发生内部错误: {str(e)}")

@app.get("/", tags=["健康检查"])
async def root():
    return {"message": "Time-LLM 预测服务正在运行！"}
