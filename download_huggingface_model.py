import os
from huggingface_hub import snapshot_download
import sys


def download_model(model_id, local_dir, endpoint_url=None):
    """
    从 Hugging Face Hub (或其镜像) 下载模型到指定的本地目录。

    :param model_id: Hugging Face上的模型ID, 例如 'huggyllama/llama-7b'
    :param local_dir: 本地存储路径, 例如 '/path/to/your/models/llama-7b'
    :param endpoint_url: (可选) Hugging Face镜像地址, 例如 'https://hf-mirror.com'
    """
    # 如果提供了镜像地址，则设置环境变量
    if endpoint_url:
        print(f"正在设置Hugging Face镜像地址: {endpoint_url}")
        os.environ['HF_ENDPOINT'] = endpoint_url

    # 确认当前使用的Endpoint
    # os.getenv会读取环境变量，如果不存在则返回None
    current_endpoint = os.getenv("HF_ENDPOINT", "https://huggingface.co (默认)")
    print(f"当前使用的下载端点: {current_endpoint}")

    # 展开用户目录（~）
    local_dir = os.path.expanduser(local_dir)

    # 确保目标目录存在，如果不存在则创建
    if not os.path.exists(local_dir):
        print(f"目录 {local_dir} 不存在，正在创建...")
        os.makedirs(local_dir)
        print("目录创建成功。")

    print(f"\n开始下载模型: {model_id}")
    print(f"将要保存到: {local_dir}")
    print("下载过程可能需要一些时间，请耐心等待...")

    try:
        # 使用 snapshot_download 下载整个模型仓库
        snapshot_download(
            repo_id=model_id,
            local_dir=local_dir,
            # 如果下载私有或受限模型，依然需要token
            # token="hf_YOUR_TOKEN_HERE"
        )
        print("\n模型下载完成！")
        print(f"所有文件已成功保存到: {local_dir}")

    except Exception as e:
        print(f"\n下载过程中发生错误: {e}", file=sys.stderr)
        print("\n请检查：", file=sys.stderr)
        print("1. 你的网络连接是否正常，或者镜像地址是否可用。", file=sys.stderr)
        print(f"2. 模型ID '{model_id}' 是否正确。", file=sys.stderr)
        print(f"3. 目标路径 '{local_dir}' 是否有写入权限和足够的磁盘空间。", file=sys.stderr)


if __name__ == '__main__':
    # --- 用户配置区 ---

    # 1. Hugging Face 镜像地址
    # 如果你不需要使用镜像，请将此行注释掉，或者将值设为 None
    HF_MIRROR_ENDPOINT = "https://hf-mirror.com"
    # 其他可用镜像, 例如:
    # HF_MIRROR_ENDPOINT = "https://huggingface.co" # 官方地址
    # HF_MIRROR_ENDPOINT = "https://hf.my-mirror.com" # 你的其他镜像

    # 2. 你要下载的模型的ID
    #HF_MODEL_ID = "huggyllama/llama-7b"
    HF_MODEL_ID = "google-bert/bert-base-uncased"

    # 3. 你希望将模型保存到的本地路径 (根据你的环境修改)
    #LOCAL_MODEL_PATH = "/root/autodl-tmp/Time-LLM/llm_models/llama-7b"
    LOCAL_MODEL_PATH = "/root/autodl-tmp/Time-LLM/llm_models/bert-base-uncased"
    # --- 执行下载 ---
    download_model(
        model_id=HF_MODEL_ID,
        local_dir=LOCAL_MODEL_PATH,
        endpoint_url=HF_MIRROR_ENDPOINT
    )