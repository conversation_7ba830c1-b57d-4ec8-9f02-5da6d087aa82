# file: download_model.py
# 最终稳健版：回归到“先下载到临时缓存，再用最可靠的方式复制”的方案，以绕开 modelscope 库的内部 bug。

import os
import shutil
from modelscope.hub.snapshot_download import snapshot_download

def main():
    # --- 1. 定义路径 ---
    model_id = 'qwen/Qwen2.5-7B-Instruct'
    project_root = os.path.dirname(os.path.abspath(__file__))
    
    # 我们期望的最终模型存放目录
    final_model_dir = os.path.join(project_root, 'llm_models', 'Qwen2.5-7B-Instruct')
    # 临时下载目录
    temp_download_dir = os.path.join(project_root, 'llm_models', 'temp_download_dir')

    print("="*80)
    print("开始智能下载流程 (最终稳健版)")
    print(f"最终目标目录: {final_model_dir}")
    print("="*80)

    # --- 2. 下载前清理 ---
    print(f"[准备] 确保临时目录 '{temp_download_dir}' 是干净的...")
    if os.path.exists(temp_download_dir):
        shutil.rmtree(temp_download_dir)
    os.makedirs(temp_download_dir)

    try:
        # --- 3. 使用 cache_dir 下载模型到临时目录 ---
        print(f"\n[步骤 1/3] 正在下载模型 '{model_id}' 到临时缓存区...")
        # 这是 modelscope 最稳定可靠的下载方式
        snapshot_download(model_id=model_id, cache_dir=temp_download_dir)
        print(" -> 模型文件下载请求完成。")

        # --- 4. 验证下载内容并找到源目录 ---
        print(f"\n[步骤 2/3] 正在验证下载内容...")
        # ModelScope 会在 cache_dir 下根据 model_id 创建子目录
        model_id_as_path = model_id.replace('/', os.sep)
        source_dir = os.path.join(temp_download_dir, model_id_as_path)
        
        print(f" -> 检查下载好的源目录: {source_dir}")
        if not os.path.exists(source_dir) or not os.listdir(source_dir):
            raise RuntimeError(f"下载失败：源目录 '{source_dir}' 不存在或为空！")

        print(" -> 源目录存在且不为空，验证通过。")

        # --- 5. 使用最可靠的方式复制文件 ---
        print(f"\n[步骤 3/3] 正在将文件复制到最终目录: {final_model_dir}")
        
        # 确保最终目录是干净的
        if os.path.exists(final_model_dir):
            shutil.rmtree(final_model_dir)
        
        # 使用 shutil.copytree 递归复制整个目录树，这是最稳妥的方式
        shutil.copytree(source_dir, final_model_dir)
        
        print(f" -> 文件已成功复制。")
        
        # --- 清理工作 ---
        print(f"\n[收尾] 正在清理临时文件...")
        shutil.rmtree(temp_download_dir)
        print(" -> 清理完成。")

        print("\n" + "="*80)
        print("🎉 任务成功完成！")
        print(f"模型已准备就绪，位于: {final_model_dir}")
        final_files = os.listdir(final_model_dir)
        print(f" -> 最终目录包含 {len(final_files)} 个文件/文件夹。")
        print("="*80)

    except Exception as e:
        print(f"\n[致命错误] 在执行过程中发生异常: {e}")
        # 如果出错，也尝试清理临时文件夹
        if os.path.exists(temp_download_dir):
            shutil.rmtree(temp_download_dir)

if __name__ == '__main__':
    main()
