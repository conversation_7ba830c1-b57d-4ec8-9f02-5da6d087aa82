# file: preprocess_gas_data.py
# 最终修正版：确保所有路径都基于当前脚本位置，绝对不会混淆。

import pandas as pd
import os

# --- 1. 定义清晰的路径 ---
# 获取当前脚本所在的目录，也就是我们新的、正确的 /root/autodl-tmp/Time-LLM/
project_root = os.path.dirname(os.path.abspath(__file__))

# 构造原始数据文件的绝对路径
original_file_path = os.path.join(project_root, 'dataset', 'gas', 'gas_concentration.csv')

# 构造处理后新文件的绝对路径
processed_file_path = os.path.join(project_root, 'dataset', 'gas', 'gas_custom.csv')

# --- 2. 执行数据处理 ---
print("="*80)
print(f"准备读取原始数据: {original_file_path}")
print(f"将要保存处理后数据到: {processed_file_path}")
print("="*80)

# 检查原始文件是否存在于正确的位置
if not os.path.exists(original_file_path):
    print(f"错误：在正确的位置找不到原始数据文件！")
    print(f"请确认 '{original_file_path}' 这个文件存在。")
    print("您可能需要运行 'mv' 命令将数据从 _broken 文件夹移动过来。")
    exit()

try:
    df = pd.read_csv(original_file_path, engine='python', encoding='gbk')
except Exception as e:
    print(f"错误：读取CSV文件时失败。错误信息: {e}")
    exit()

new_columns = [
    'date',
    '66081101194701MN0001001A090', '66081101194701MN0001002A010', '66081101194701MN0001002A040',
    '66081101194701MN0001003A010', '66081101194701MN0001003A020', '66081101194701MN0001003A030',
    '66081101194701MN0001003A050', '66081101194701MN0001005A080', '66081101194701MN0001005A090',
    '66081101194701MN0001005A130', '66081101194701MN0001008A150', '66081101194701MN0001009A070',
    '66081101194701MN0001009A130', '66081101194701MN0001010A010', '66081101194701MN0001010A090',
    '66081101194701MN0001012A010', '66081101194701MN0001012A050', '66081101194701MN0001012A110',
    '66081101194701MN0001012A130', '66081101194701MN0001013A090', '66081101194701MN0001015A090',
    '66081101194701MN0001015A130', '66081101194701MN0001017A110', '66081101194701MN0001017A160',
    '66081101194701MN0001018A090', '66081101194701MN0001018A100', '66081101194701MN0001018A130',
    '66081101194701MN0001021A080', '66081101194701MN0001023A090', '66081101194701MN0001023A130'
]

if len(df.columns) != len(new_columns):
    raise ValueError(f"列数不匹配! CSV文件有 {len(df.columns)} 列, 但代码里定义了 {len(new_columns)} 列。")

df.columns = new_columns
df['date'] = pd.to_datetime(df['date'])

if df.isnull().values.any():
    print("检测到缺失值，正在填充...")
    df.fillna(method='ffill', inplace=True)
    df.fillna(0, inplace=True)
    print("缺失值填充完毕。")

df.to_csv(processed_file_path, index=False)

print("\n" + "="*80)
print(f"🎉 数据预处理成功！")
print(f"新的数据文件 'gas_custom.csv' 已保存在正确的位置: {processed_file_path}")
print("="*80)
