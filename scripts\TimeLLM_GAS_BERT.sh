model_name=TimeLLM
train_epochs=50
dropout=0.1
learning_rate=0.0005


master_port=00097
num_process=1
batch_size=8
llm_model='BERT'
llm_dim=768
llm_layers=12
d_model=64
d_ff=256

enc_in=6
dec_in=6
c_out=6

comment='TimeLLM-GAS'
target='GAS_0'

n_heads=8
e_layers=2
d_layers=1
factor=1
embed='timeF'

prompt_domain=1
content='The dataset records gas concentration every minute, comprising 6 indicators representing the gas concentration levels at different monitoring points.'
patch_len=16

des='Gas_Prediction_bert'


accelerate launch --mixed_precision bf16 --num_processes $num_process --main_process_port $master_port run_main.py \
  --task_name long_term_forecast \
  --is_training 1 \
  --root_path ./dataset/gas/ \
  --data_path gas_custom_bert.csv \
  --model_id GASm1_120_20_30 \
  --model $model_name \
  --data custom \
  --features M \
  --seq_len 120 \
  --label_len 20 \
  --pred_len 30 \
  --enc_in $enc_in \
  --dec_in $dec_in \
  --c_out $c_out \
  --llm_model $llm_model \
  --d_model $d_model \
  --d_ff $d_ff \
  --batch_size $batch_size \
  --dropout $dropout \
  --learning_rate $learning_rate \
  --llm_layers $llm_layers \
  --train_epochs $train_epochs \
  --model_comment $comment \
  --target $target \
  --n_heads $n_heads\
  --e_layers $e_layers \
  --d_layers $d_layers \
  --factor $factor \
  --embed $embed \
  --prompt_domain $prompt_domain \
  --patch_len $patch_len \
  --llm_dim $llm_dim \
  --des $des

#accelerate launch --multi_gpu --mixed_precision bf16 --num_processes $num_process --main_process_port $master_port run_main.py \
#  --task_name long_term_forecast \
#  --is_training 1 \
#  --root_path ./dataset/weather/ \
#  --data_path weather.csv \
#  --model_id weather_512_192 \
#  --model $model_name \
#  --data Weather \
#  --features M \
#  --seq_len 512 \
#  --label_len 48 \
#  --pred_len 192 \
#  --e_layers 2 \
#  --d_layers 1 \
#  --factor 3 \
#  --enc_in 21 \
#  --dec_in 21 \
#  --c_out 21 \
#  --d_model 32 \
#  --d_ff 32 \
#  --batch_size $batch_size \
#  --learning_rate $learning_rate \
#  --llm_layers $llm_layers \
#  --train_epochs $train_epochs \
#  --model_comment $comment
#
#  accelerate launch --multi_gpu --mixed_precision bf16 --num_processes $num_process --main_process_port $master_port run_main.py \
#  --task_name long_term_forecast \
#  --is_training 1 \
#  --root_path ./dataset/weather/ \
#  --data_path weather.csv \
#  --model_id weather_512_336 \
#  --model $model_name \
#  --data Weather \
#  --features M \
#  --seq_len 512 \
#  --label_len 48 \
#  --pred_len 336 \
#  --e_layers 2 \
#  --d_layers 1 \
#  --factor 3 \
#  --enc_in 21 \
#  --dec_in 21 \
#  --c_out 21 \
#  --d_model 32 \
#  --d_ff 128 \
#  --batch_size $batch_size \
#  --learning_rate $learning_rate \
#  --llm_layers $$llm_layers \
#  --train_epochs 10 \
#  --model_comment $comment
#
#  accelerate launch --multi_gpu --mixed_precision bf16 --num_processes $num_process --main_process_port $master_port run_main.py \
#  --task_name long_term_forecast \
#  --is_training 1 \
#  --root_path ./dataset/weather/ \
#  --data_path weather.csv \
#  --model_id weather_512_720 \
#  --model $model_name \
#  --data Weather \
#  --features M \
#  --seq_len 512 \
#  --label_len 48 \
#  --pred_len 720 \
#  --e_layers 2 \
#  --d_layers 1 \
#  --factor 3 \
#  --enc_in 21 \
#  --dec_in 21 \
#  --c_out 21 \
#  --d_model 32 \
#  --d_ff 128 \
#  --batch_size $batch_size \
#  --learning_rate $learning_rate \
#  --llm_layers $$llm_layers \
#  --train_epochs 15 \
#  --model_comment $comment
