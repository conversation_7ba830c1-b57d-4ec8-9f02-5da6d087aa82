#!/bin/bash
# 最终修正版: 移除了 run_main.py 不识别的 llm_model_id, use_gpu, gpu 参数

export task_name=long_term_forecast
export model_name=TimeLLM
export is_training=1
export model_comment='Gas_Prediction_bert'
export root_path_name=./dataset/
export data_path_name=gas/gas_custom_bert.csv
export data_name=custom
export features=M
export target='GAS_0'
export freq=t
export seq_len=120
export pred_len=30
export label_len=20
export d_model=64
export d_ff=256
export batch_size=8
export learning_rate=0.0005
export train_epochs=50
export patience=3
export enc_in=6
export dec_in=6
export c_out=6
export model_id_name=custom_120_20_30
# 注意：llm_model 这个参数是存在的，但 llm_model_id 不存在。
# 这意味着程序可能会有其他方式去找到我们下载的模型，或者它会尝试在线下载。
# 我们先保留 llm_model 参数，看看程序的行为。
export llm_model=BERT
# llm_dim 可能也不需要，但我们先保留它，因为它不报错
export llm_dim=768
export llm_layers=12


export n_heads=8
export e_layers=2
export d_layers=1
export factor=1
export embed='timeF'
export des='Gas_Prediction_bert'

echo "--- 开始最终的瓦斯浓度预测任务 (参数精简版) ---"

python -u run_main.py   --task_name $task_name   --is_training $is_training   --root_path $root_path_name   --data_path $data_path_name   --model_id ${model_id_name}_${seq_len}_${pred_len}   --model $model_name   --data $data_name   --features $features   --target $target   --freq $freq   --seq_len $seq_len   --label_len $label_len   --pred_len $pred_len   --d_model $d_model   --d_ff $d_ff   --batch_size $batch_size   --learning_rate $learning_rate   --train_epochs $train_epochs   --patience $patience   --enc_in $enc_in   --dec_in $dec_in   --c_out $c_out   --itr 1   --model_comment $model_comment   --llm_model $llm_model   --llm_dim $llm_dim  --llm_layers $llm_layers    --n_heads $n_heads  --e_layers $e_layers  --d_layers $d_layers  --factor $factor  --embed $embed  --des $des


